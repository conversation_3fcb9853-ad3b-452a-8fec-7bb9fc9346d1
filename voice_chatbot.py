import streamlit as st
import os
from groq import Groq
import pyttsx3
import speech_recognition as sr
from dotenv import load_dotenv
import time


load_dotenv()


def inject_css():
    st.markdown("""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

    /* Enhanced dark theme with gradient background */
    [data-testid="stAppViewContainer"] {
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
        color: #ffffff;
        font-family: 'Rajdhani', sans-serif;
    }

    /* Animated background gradient */
    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Enhanced sidebar styling */
    [data-testid="stSidebar"] {
        background: linear-gradient(180deg, rgba(10,10,10,0.95) 0%, rgba(26,26,46,0.95) 100%);
        border-right: 2px solid rgba(0,255,255,0.3);
    }

    /* Main content area */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
        max-width: 1200px;
    }

    /* Enhanced chat bubbles with glassmorphism */
    [data-testid="stChatMessage"] {
        border-radius: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.1);
        margin: 12px 0;
        padding: 0;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    [data-testid="stChatMessage"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,255,255,0.2);
    }

    /* User message style with enhanced glassmorphism */
    [data-testid="stChatMessage"][data-testid*="user"] {
        background: linear-gradient(135deg, rgba(0,255,255,0.1) 0%, rgba(0,150,255,0.05) 100%);
        border-left: 4px solid #00ffff;
        box-shadow: 0 4px 15px rgba(0,255,255,0.2);
    }

    /* Assistant message style */
    [data-testid="stChatMessage"][data-testid*="assistant"] {
        background: linear-gradient(135deg, rgba(0,255,170,0.1) 0%, rgba(0,200,100,0.05) 100%);
        border-left: 4px solid #00ffaa;
        box-shadow: 0 4px 15px rgba(0,255,170,0.2);
    }

    /* Enhanced buttons with multiple states */
    .stButton>button {
        border-radius: 30px;
        background: linear-gradient(135deg, rgba(0,255,255,0.1) 0%, rgba(0,150,255,0.1) 100%);
        border: 2px solid rgba(0,255,255,0.5);
        color: #00ffff;
        font-weight: 600;
        font-family: 'Orbitron', monospace;
        padding: 12px 24px;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }

    .stButton>button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .stButton>button:hover::before {
        left: 100%;
    }

    .stButton>button:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 10px 25px rgba(0,255,255,0.4);
        border-color: #00ffff;
        background: linear-gradient(135deg, rgba(0,255,255,0.2) 0%, rgba(0,150,255,0.2) 100%);
    }

    .stButton>button:active {
        transform: translateY(-1px) scale(1.02);
    }

    /* Enhanced text input */
    .stTextInput>div>div>input {
        background: linear-gradient(135deg, rgba(30,30,30,0.9) 0%, rgba(45,45,45,0.9) 100%);
        color: #ffffff;
        border: 2px solid rgba(0,255,255,0.3);
        border-radius: 20px;
        padding: 12px 20px;
        font-family: 'Rajdhani', sans-serif;
        font-size: 16px;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .stTextInput>div>div>input:focus {
        border-color: #00ffff;
        box-shadow: 0 0 20px rgba(0,255,255,0.3);
        outline: none;
    }

    /* Enhanced checkbox styling */
    .stCheckbox>label {
        color: #ffffff !important;
        font-family: 'Rajdhani', sans-serif;
        font-weight: 500;
    }

    /* Advanced Siri-like pulse animation */
    @keyframes advancedPulse {
        0% {
            transform: scale(1) rotate(0deg);
            box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.7),
                        0 0 0 0 rgba(0, 150, 255, 0.5),
                        0 0 0 0 rgba(0, 100, 255, 0.3);
        }
        25% {
            transform: scale(1.05) rotate(90deg);
        }
        50% {
            transform: scale(1.1) rotate(180deg);
            box-shadow: 0 0 0 20px rgba(0, 255, 255, 0),
                        0 0 0 40px rgba(0, 150, 255, 0),
                        0 0 0 60px rgba(0, 100, 255, 0);
        }
        75% {
            transform: scale(1.05) rotate(270deg);
        }
        100% {
            transform: scale(1) rotate(360deg);
            box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.7),
                        0 0 0 0 rgba(0, 150, 255, 0.5),
                        0 0 0 0 rgba(0, 100, 255, 0.3);
        }
    }

    .siri-pulse {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: conic-gradient(from 0deg, #00ffff, #0077ff, #00ffaa, #00ffff);
        display: flex;
        justify-content: center;
        align-items: center;
        animation: advancedPulse 3s infinite;
        margin: 20px auto;
        position: relative;
        backdrop-filter: blur(10px);
    }

    .siri-pulse::before {
        content: '';
        position: absolute;
        width: 80%;
        height: 80%;
        border-radius: 50%;
        background: radial-gradient(circle, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.6) 100%);
    }

    .siri-icon {
        font-size: 45px;
        color: white;
        z-index: 1;
        text-shadow: 0 0 10px rgba(0,255,255,0.8);
    }

    /* Floating particles animation */
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-10px) rotate(120deg); }
        66% { transform: translateY(5px) rotate(240deg); }
    }

    .floating-particle {
        position: fixed;
        width: 4px;
        height: 4px;
        background: rgba(0,255,255,0.6);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
        pointer-events: none;
        z-index: -1;
    }

    /* Title styling */
    h1 {
        font-family: 'Orbitron', monospace !important;
        font-weight: 900 !important;
        background: linear-gradient(45deg, #00ffff, #0077ff, #00ffaa);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-align: center;
        text-shadow: 0 0 30px rgba(0,255,255,0.5);
        margin-bottom: 2rem !important;
    }

    /* Scrollbar styling */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(0,0,0,0.3);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, #00ffff, #0077ff);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(180deg, #00ffaa, #00ffff);
    }
    </style>
    """, unsafe_allow_html=True)

# Initialize components
client = Groq(api_key=os.getenv("GROQ_API_KEY"))
recognizer = sr.Recognizer()

# Enhanced Streamlit UI setup with futuristic theme
inject_css()

# Add floating particles for ambiance
st.markdown("""
<div id="particles-container"></div>
<script>
function createParticles() {
    const container = document.getElementById('particles-container');
    if (!container) return;

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
        container.appendChild(particle);
    }
}
createParticles();
</script>
""", unsafe_allow_html=True)

# Enhanced title with subtitle
st.markdown("""
<div style="text-align: center; margin-bottom: 3rem;">
    <h1 style="
        font-size: 3.5rem;
        margin-bottom: 0.5rem;
        background: linear-gradient(45deg, #00ffff, #0077ff, #00ffaa, #ff00ff);
        background-size: 400% 400%;
        animation: gradientShift 3s ease infinite;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-family: 'Orbitron', monospace;
        font-weight: 900;
        text-shadow: 0 0 30px rgba(0,255,255,0.5);
    ">
        🌀 NEXUS AI VOICE ASSISTANT
    </h1>
    <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        margin-top: 1rem;
        padding: 15px 30px;
        background: linear-gradient(135deg, rgba(0,255,255,0.1) 0%, rgba(0,150,255,0.05) 100%);
        border-radius: 50px;
        border: 1px solid rgba(0,255,255,0.3);
        backdrop-filter: blur(10px);
        max-width: 500px;
        margin: 1rem auto;
    ">
        <span style="font-size: 28px; animation: pulse 2s infinite;">🚀</span>
        <span style="
            color: #00ffff;
            font-family: 'Rajdhani', sans-serif;
            font-size: 1.2rem;
            font-weight: 600;
            text-shadow: 0 0 10px rgba(0,255,255,0.5);
        ">
            Advanced AI • Voice Recognition • Real-time Processing
        </span>
        <span style="font-size: 28px; animation: pulse 2s infinite 1s;">⚡</span>
    </div>
</div>
""", unsafe_allow_html=True)

# Initialize text-to-speech engine
def init_tts():
    try:
        engine = pyttsx3.init()
        engine.setProperty('rate', 150)
        engine.setProperty('voice', 'english')  # More natural voice
        return engine
    except:
        return None

# Session state for conversation and auto-mode
if 'conversation' not in st.session_state:
    st.session_state.conversation = []
if 'auto_mode' not in st.session_state:
    st.session_state.auto_mode = True
if 'listening' not in st.session_state:
    st.session_state.listening = False
if 'running' not in st.session_state:
    st.session_state.running = True

# Voice input function with auto-detection
def get_voice_input():
    st.session_state.listening = True
    with sr.Microphone() as source:
        recognizer.adjust_for_ambient_noise(source)
        try:
            audio = recognizer.listen(source, timeout=5)
            query = recognizer.recognize_google(audio)
            st.session_state.listening = False
            return query
        except Exception as e:
            st.session_state.listening = False
            return None

# Get Groq response with natural name integration
def get_groq_response(prompt):
    try:
        response = client.chat.completions.create(
            messages=[{"role": "user", "content": prompt}],
            model="llama3-8b-8192"
        )
        return response.choices[0].message.content
    except Exception as e:
        return f"Error: {str(e)}"

# Text-to-speech function
def speak(text):
    engine = init_tts()
    if engine:
        engine.say(text)
        engine.runAndWait()

# Main chat interface
chat_container = st.container()

# Enhanced auto-listening logic with status indicators
if st.session_state.auto_mode and st.session_state.running:
    # Enhanced listening animation with status
    if st.session_state.listening:
        st.markdown("""
        <div style="text-align: center; margin: 30px 0; padding: 20px;">
            <div class="siri-pulse">
                <span class="siri-icon">�</span>
            </div>
            <div style="margin-top: 20px;">
                <h3 style="
                    color: #00ffff;
                    font-family: 'Orbitron', monospace;
                    font-weight: 700;
                    margin-bottom: 10px;
                    text-shadow: 0 0 15px rgba(0,255,255,0.8);
                ">🎯 LISTENING FOR VOICE INPUT</h3>
                <p style="
                    color: #00ffaa;
                    font-family: 'Rajdhani', sans-serif;
                    font-size: 1.1rem;
                    margin: 0;
                    opacity: 0.8;
                ">Speak clearly into your microphone...</p>
                <div style="
                    margin-top: 15px;
                    display: flex;
                    justify-content: center;
                    gap: 5px;
                ">
                    <div style="
                        width: 8px; height: 8px;
                        background: #00ffff;
                        border-radius: 50%;
                        animation: pulse 1s infinite;
                    "></div>
                    <div style="
                        width: 8px; height: 8px;
                        background: #00ffff;
                        border-radius: 50%;
                        animation: pulse 1s infinite 0.2s;
                    "></div>
                    <div style="
                        width: 8px; height: 8px;
                        background: #00ffff;
                        border-radius: 50%;
                        animation: pulse 1s infinite 0.4s;
                    "></div>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    # Auto-detect voice input
    query = get_voice_input()
    if query:
        st.session_state.user_input = query
        if st.session_state.user_input:
            with st.spinner("🌀 Processing..."):
                response = get_groq_response(st.session_state.user_input)
                st.session_state.conversation.append(("You", st.session_state.user_input))
                st.session_state.conversation.append(("Assistant", response))
                speak(response)  # Auto-speak response
                time.sleep(0.5)
                st.rerun()

# Enhanced conversation display with modern chat bubbles
with chat_container:
    if not st.session_state.conversation:
        # Welcome message when no conversation exists
        st.markdown("""
        <div style="
            text-align: center;
            padding: 40px 20px;
            margin: 20px 0;
            background: linear-gradient(135deg, rgba(0,255,255,0.05) 0%, rgba(0,150,255,0.05) 100%);
            border-radius: 20px;
            border: 1px solid rgba(0,255,255,0.2);
            backdrop-filter: blur(10px);
        ">
            <div style="font-size: 4rem; margin-bottom: 1rem;">🤖</div>
            <h2 style="
                color: #00ffff;
                font-family: 'Orbitron', monospace;
                margin-bottom: 1rem;
                text-shadow: 0 0 15px rgba(0,255,255,0.5);
            ">Welcome to Nexus AI</h2>
            <p style="
                color: #ffffff;
                font-family: 'Rajdhani', sans-serif;
                font-size: 1.2rem;
                opacity: 0.8;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            ">
                Your advanced AI voice assistant is ready to help.
                <br>🎤 <strong>Voice Mode:</strong> Simply speak and I'll respond
                <br>⌨️ <strong>Text Mode:</strong> Type your questions below
                <br>🚀 <strong>Features:</strong> Real-time processing, natural conversations, and intelligent responses
            </p>
        </div>
        """, unsafe_allow_html=True)

    for i, (speaker, text) in enumerate(st.session_state.conversation):
        if speaker == "You":
            st.chat_message("user").markdown(f"""
            <div style="
                padding: 18px 24px;
                border-radius: 25px 25px 5px 25px;
                background: linear-gradient(135deg, rgba(0,255,255,0.15) 0%, rgba(0,150,255,0.1) 100%);
                border: 1px solid rgba(0,255,255,0.3);
                backdrop-filter: blur(15px);
                margin: 10px 0;
                position: relative;
                box-shadow: 0 8px 20px rgba(0,255,255,0.1);
                transition: all 0.3s ease;
            ">
                <div style="
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    margin-bottom: 8px;
                ">
                    <span style="
                        font-size: 1.2rem;
                        background: linear-gradient(45deg, #00ffff, #0077ff);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    ">👤</span>
                    <strong style="
                        color: #00ffff;
                        font-family: 'Orbitron', monospace;
                        font-size: 0.9rem;
                        text-shadow: 0 0 10px rgba(0,255,255,0.5);
                    ">YOU</strong>
                </div>
                <div style="
                    color: #ffffff;
                    font-family: 'Rajdhani', sans-serif;
                    font-size: 1.1rem;
                    line-height: 1.5;
                    font-weight: 400;
                ">{text}</div>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.chat_message("assistant").markdown(f"""
            <div style="
                padding: 18px 24px;
                border-radius: 25px 25px 25px 5px;
                background: linear-gradient(135deg, rgba(0,255,170,0.15) 0%, rgba(0,200,100,0.1) 100%);
                border: 1px solid rgba(0,255,170,0.3);
                backdrop-filter: blur(15px);
                margin: 10px 0;
                position: relative;
                box-shadow: 0 8px 20px rgba(0,255,170,0.1);
                transition: all 0.3s ease;
            ">
                <div style="
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    margin-bottom: 8px;
                ">
                    <span style="
                        font-size: 1.2rem;
                        background: linear-gradient(45deg, #00ffaa, #00ff77);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    ">🤖</span>
                    <strong style="
                        color: #00ffaa;
                        font-family: 'Orbitron', monospace;
                        font-size: 0.9rem;
                        text-shadow: 0 0 10px rgba(0,255,170,0.5);
                    ">NEXUS AI</strong>
                </div>
                <div style="
                    color: #ffffff;
                    font-family: 'Rajdhani', sans-serif;
                    font-size: 1.1rem;
                    line-height: 1.5;
                    font-weight: 400;
                ">{text}</div>
            </div>
            """, unsafe_allow_html=True)

# Enhanced sidebar with modern design
with st.sidebar:
    # Header section
    st.markdown("""
    <div style="
        padding: 20px;
        border-radius: 20px;
        background: linear-gradient(135deg, rgba(0,255,255,0.1) 0%, rgba(0,150,255,0.05) 100%);
        border: 1px solid rgba(0,255,255,0.3);
        backdrop-filter: blur(15px);
        margin-bottom: 25px;
        text-align: center;
    ">
        <div style="font-size: 2.5rem; margin-bottom: 10px;">⚙️</div>
        <h2 style="
            color: #00ffff;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 0 15px rgba(0,255,255,0.5);
        ">CONTROL PANEL</h2>
        <p style="
            color: #ffffff;
            font-family: 'Rajdhani', sans-serif;
            margin: 5px 0 0 0;
            opacity: 0.7;
            font-size: 0.9rem;
        ">Configure your AI assistant</p>
    </div>
    """, unsafe_allow_html=True)

    # Settings section
    st.markdown("""
    <div style="
        padding: 15px;
        border-radius: 15px;
        background: linear-gradient(135deg, rgba(0,255,170,0.1) 0%, rgba(0,200,100,0.05) 100%);
        border: 1px solid rgba(0,255,170,0.3);
        backdrop-filter: blur(10px);
        margin-bottom: 20px;
    ">
        <h3 style="
            color: #00ffaa;
            font-family: 'Orbitron', monospace;
            margin-bottom: 15px;
            font-size: 1.1rem;
        ">🎛️ SETTINGS</h3>
    </div>
    """, unsafe_allow_html=True)

    st.session_state.auto_mode = st.checkbox("🎤 Auto Voice Mode", value=True, help="Automatically listen for voice input")
    voice_enabled = st.checkbox("🔊 Enable Voice Responses", value=True, help="AI will speak responses aloud")

    # Action buttons
    st.markdown("<br>", unsafe_allow_html=True)

    col1, col2 = st.columns(2)
    with col1:
        if st.button("🗑️ Clear", key="clear_btn", help="Clear conversation history"):
            st.session_state.conversation = []
            st.rerun()

    with col2:
        if st.button("🔄 Restart", key="restart_btn", help="Restart the session"):
            st.session_state.running = True
            st.rerun()

    # Status indicator
    status_color = "#00ffaa" if st.session_state.running else "#ff6b6b"
    status_text = "ONLINE" if st.session_state.running else "OFFLINE"
    status_icon = "🟢" if st.session_state.running else "🔴"

    st.markdown(f"""
    <div style="
        padding: 12px;
        border-radius: 12px;
        background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(30,30,30,0.3) 100%);
        border: 1px solid {status_color}40;
        margin: 20px 0;
        text-align: center;
    ">
        <div style="
            color: {status_color};
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            font-size: 0.9rem;
        ">
            {status_icon} STATUS: {status_text}
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Conversation history section
    if st.session_state.conversation:
        st.markdown("""
        <div style="
            padding: 15px;
            border-radius: 15px;
            background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(100,100,100,0.05) 100%);
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            margin-top: 25px;
        ">
            <h3 style="
                color: #ffffff;
                font-family: 'Orbitron', monospace;
                margin-bottom: 15px;
                font-size: 1.1rem;
            ">💾 RECENT HISTORY</h3>
        </div>
        """, unsafe_allow_html=True)

        # Show last 3 messages for sidebar
        recent_messages = st.session_state.conversation[-3:]
        for i, (speaker, text) in enumerate(recent_messages):
            # Truncate long messages for sidebar
            display_text = text[:50] + "..." if len(text) > 50 else text

            if speaker == "You":
                st.markdown(f"""
                <div style="
                    padding: 12px;
                    margin: 8px 0;
                    border-radius: 12px;
                    background: linear-gradient(135deg, rgba(0,255,255,0.1) 0%, rgba(0,150,255,0.05) 100%);
                    border-left: 3px solid #00ffff;
                    border: 1px solid rgba(0,255,255,0.2);
                    backdrop-filter: blur(5px);
                ">
                    <div style="
                        color: #00ffff;
                        font-family: 'Orbitron', monospace;
                        font-size: 0.8rem;
                        font-weight: 600;
                        margin-bottom: 5px;
                    ">👤 YOU</div>
                    <div style="
                        color: #ffffff;
                        font-family: 'Rajdhani', sans-serif;
                        font-size: 0.9rem;
                        line-height: 1.3;
                        opacity: 0.9;
                    ">{display_text}</div>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div style="
                    padding: 12px;
                    margin: 8px 0;
                    border-radius: 12px;
                    background: linear-gradient(135deg, rgba(0,255,170,0.1) 0%, rgba(0,200,100,0.05) 100%);
                    border-left: 3px solid #00ffaa;
                    border: 1px solid rgba(0,255,170,0.2);
                    backdrop-filter: blur(5px);
                ">
                    <div style="
                        color: #00ffaa;
                        font-family: 'Orbitron', monospace;
                        font-size: 0.8rem;
                        font-weight: 600;
                        margin-bottom: 5px;
                    ">🤖 AI</div>
                    <div style="
                        color: #ffffff;
                        font-family: 'Rajdhani', sans-serif;
                        font-size: 0.9rem;
                        line-height: 1.3;
                        opacity: 0.9;
                    ">{display_text}</div>
                </div>
                """, unsafe_allow_html=True)

# Enhanced manual input section
if not st.session_state.auto_mode or not st.session_state.running:
    st.markdown("""
    <div style="
        padding: 25px;
        border-radius: 20px;
        background: linear-gradient(135deg, rgba(0,255,255,0.05) 0%, rgba(0,150,255,0.05) 100%);
        border: 1px solid rgba(0,255,255,0.2);
        backdrop-filter: blur(15px);
        margin: 30px 0;
    ">
        <h3 style="
            color: #00ffff;
            font-family: 'Orbitron', monospace;
            text-align: center;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 0 0 15px rgba(0,255,255,0.5);
        ">⌨️ TEXT INPUT MODE</h3>
        <p style="
            color: #ffffff;
            font-family: 'Rajdhani', sans-serif;
            text-align: center;
            margin-bottom: 20px;
            opacity: 0.8;
            font-size: 1.1rem;
        ">Type your message below and click Send to interact with Nexus AI</p>
    </div>
    """, unsafe_allow_html=True)

    # Enhanced input form
    with st.form(key="message_form", clear_on_submit=True):
        user_input = st.text_input(
            "💬 Your Message:",
            key="manual_input",
            placeholder="Type your question or message here...",
            help="Enter your message and press Enter or click Send"
        )

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            submitted = st.form_submit_button(
                "🚀 Send Message",
                use_container_width=True,
                help="Send your message to Nexus AI"
            )

        if submitted and user_input:
            with st.spinner("🌀 Processing your request..."):
                response = get_groq_response(user_input)
                st.session_state.conversation.append(("You", user_input))
                st.session_state.conversation.append(("Assistant", response))
                if voice_enabled:
                    speak(response)
                st.rerun()

# Footer with additional information
st.markdown("""
<div style="
    margin-top: 50px;
    padding: 20px;
    border-radius: 15px;
    background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(30,30,30,0.3) 100%);
    border: 1px solid rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    text-align: center;
">
    <p style="
        color: #ffffff;
        font-family: 'Rajdhani', sans-serif;
        margin: 0;
        opacity: 0.6;
        font-size: 0.9rem;
    ">
        🤖 Powered by <strong>Groq AI</strong> • 🎤 Voice Recognition • ⚡ Real-time Processing
        <br>
        <span style="color: #00ffff;">Nexus AI Voice Assistant</span> - Your intelligent conversation partner
    </p>
</div>
""", unsafe_allow_html=True)